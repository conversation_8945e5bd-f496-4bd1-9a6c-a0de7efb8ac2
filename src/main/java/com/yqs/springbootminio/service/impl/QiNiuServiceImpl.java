package com.yqs.springbootminio.service.impl;

import com.qiniu.common.QiniuException;
import com.qiniu.http.Response;
import com.qiniu.storage.*;
import com.qiniu.util.Auth;
import com.yqs.springbootminio.exception.BizException;
import com.yqs.springbootminio.service.FileStorageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */
//@ConditionalOnProperty(value = "qiniu.enable")
@Service
public class QiNiuServiceImpl implements FileStorageService {

    private static final Logger logger = LoggerFactory.getLogger(QiNiuServiceImpl.class);

    @Autowired
    private Auth auth;

    @Value("${qiniu.bucket-name}")
    private String bucketName;

    @Value("${qiniu.domain}")
    private String domain;

    /**
     * 检查文件是否是图片类型
     *
     * @param fileName 文件名
     * @return 是否是图片类型
     */
    private boolean isImageFile(String fileName) {
        // 支持的图片文件扩展名
        List<String> imageExtensions = Arrays.asList(
                ".jpg", ".jpeg", ".png", ".gif", ".bmp"
        );

        // 检查文件扩展名
        if (fileName != null) {
            String extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
            return imageExtensions.contains(extension);
        }

        return false;
    }

    /**
     * @param fileName
     * @param inputStream
     * @return
     */
    @Override
    public String uploadFile(String fileName, InputStream inputStream) throws Exception {

        // 检查文件是否为空
        if (fileName == null || fileName.isEmpty()) {
            throw new IllegalArgumentException("File name is empty");
        }
        // 初始化配置
        Configuration cfg = new Configuration(Region.autoRegion());
        UploadManager uploadManager = new UploadManager(cfg);
        String upToken = auth.uploadToken(bucketName);
        // 2. 关键：设置Content-Type（带字符编码）
        String mimeType = "text/plain; charset=utf-8"; // 明确指定UTF-8编码
        String fileExtension = "";
        if (fileName != null) {
            int dotIndex = fileName.lastIndexOf('.');
            if (dotIndex > 0) {
                fileExtension = fileName.substring(dotIndex);
            }
        }
        String newFileName = UUID.randomUUID().toString().replace("-", "");
        newFileName += fileExtension;
        // 上传文件
        // 检查文件是否是图片
        if (isImageFile(newFileName)) {
            Response response = uploadManager.put(inputStream.readAllBytes(), newFileName, upToken);
            // 返回文件访问 URL
            String fileUrl = "http://"+domain + "/" + newFileName;
            logger.info("File uploaded successfully. URL: {}", fileUrl);
            return newFileName;
        }
        uploadManager.put(inputStream, newFileName, upToken, null, mimeType);
        //DefaultPutRet putRet = new Gson().fromJson(response.bodyString(), DefaultPutRet.class);
        return newFileName;
    }

    /**
     * @param fileName
     */
    @Override
    public byte[] downloadFile(String fileName) {
        try {
            // 构建下载 URL
            DownloadUrl downloadUrl = new DownloadUrl(domain, false, fileName);
            String urlStr = downloadUrl.buildURL();
            URL url = new URL(urlStr);
            // 打开连接
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            // 检查响应状态码
            if (conn.getResponseCode() == 200) {
                // 读取输入流并转换为字节数组
                try (InputStream inputStream = conn.getInputStream()) {
                    return inputStream.readAllBytes();
                }
            } else {
                throw new BizException("Error downloading file from QiNiu: " + conn.getResponseMessage());
            }
        } catch (Exception e) {
            throw new BizException("Error downloading file from QiNiu", e.getMessage());
        }


    }

    @Override
    public void deleteFile(String fileName) {
        try {
            Configuration cfg = new Configuration(Region.autoRegion());
            BucketManager bucketManager = new BucketManager(auth, cfg);
            Response response = bucketManager.delete(bucketName, fileName);
            if (!response.isOK()) {
                throw new RuntimeException("Error deleting file from QiNiu: " + response.error);
            }
        } catch (QiniuException e) {
            e.printStackTrace();
            throw new RuntimeException("Error deleting file from QiNiu", e);
        }

    }


}

